#!/usr/bin/env python3
"""
FORCE LIVE TRADE EXECUTION - DERIVATIVES APPROACH
Try derivatives trading which has different minimums and confirmed permissions.
"""

import asyncio
import sys
sys.path.append('.')

async def force_derivatives_trade():
    """Execute live trade using derivatives (linear perpetuals)"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("FORCING LIVE TRADE - DERIVATIVES APPROACH")
        print("=" * 60)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # Try BTCUSDT perpetual (linear) - this has much lower minimums
        symbol = "BTCUSDT"
        category = "linear"  # Derivatives instead of spot
        
        print(f"Attempting {symbol} perpetual trade...")
        
        # Get current price
        current_price = await client.get_current_price(symbol)
        print(f"Current {symbol} Price: ${current_price:,.2f}")
        
        # For derivatives, minimum is usually much lower
        # Try with $1 worth (minimum for derivatives)
        trade_value = 1.0  # $1 USDT
        quantity = trade_value / current_price
        quantity = round(quantity, 6)
        
        print(f"Trade Value: ${trade_value} USDT")
        print(f"Quantity: {quantity:.6f} BTC")
        
        # Execute derivatives trade
        print("\nEXECUTING LIVE DERIVATIVES TRADE...")
        order_id = await client.place_order(
            symbol=symbol,
            side='Buy',
            order_type='Market',
            quantity=quantity,
            category=category  # Use linear derivatives
        )
        
        if order_id:
            print("SUCCESS: LIVE DERIVATIVES TRADE EXECUTED!")
            print(f"Order ID: {order_id}")
            print(f"Symbol: {symbol} (Perpetual)")
            print(f"Value: ${trade_value} USDT")
            print(f"Quantity: {quantity:.6f} BTC")
            
            # Check positions
            print("\nChecking positions...")
            positions = await client.get_positions(category=category, symbol=symbol)
            for pos in positions:
                if float(pos.size) > 0:
                    print(f"Position: {pos.size} {symbol}")
                    print(f"Entry Price: ${pos.avg_price}")
                    print(f"Unrealized PnL: ${pos.unrealized_pnl}")
            
            await client.close()
            return True
            
        else:
            print("FAILED: Derivatives trade not executed")
            
            # Try spot with limit order instead
            print("\nTrying SPOT with LIMIT ORDER...")
            
            # Get current bid/ask
            orderbook = await client._make_request("GET", "/v5/market/orderbook", {
                "category": "spot",
                "symbol": "BTCUSDT"
            })
            
            if orderbook.get("retCode") == 0:
                bids = orderbook["result"]["b"]
                asks = orderbook["result"]["a"]
                
                if bids and asks:
                    bid_price = float(bids[0][0])
                    ask_price = float(asks[0][0])
                    
                    # Place limit order at bid price (should fill immediately)
                    limit_price = bid_price
                    trade_value = 6.0  # $6 USDT (above minimum)
                    quantity = trade_value / limit_price
                    quantity = round(quantity, 6)
                    
                    print(f"Limit Order: {quantity:.6f} BTC at ${limit_price:,.2f}")
                    
                    order_id = await client.place_order(
                        symbol='BTCUSDT',
                        side='Buy',
                        order_type='Limit',
                        quantity=quantity,
                        price=limit_price,
                        category='spot'
                    )
                    
                    if order_id:
                        print("SUCCESS: LIVE SPOT LIMIT ORDER EXECUTED!")
                        print(f"Order ID: {order_id}")
                        await client.close()
                        return True
            
            await client.close()
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("FINAL ATTEMPT: FORCING LIVE TRADE EXECUTION")
    print("Trying derivatives and limit orders")
    print()
    
    result = asyncio.run(force_derivatives_trade())
    
    if result:
        print("\nMISSION ACCOMPLISHED: LIVE TRADE EXECUTED!")
        print("The trading system is now proven to work with real money!")
        print("Ready for autonomous profit generation!")
    else:
        print("\nCRITICAL: Still unable to execute live trades")
        print("May need manual intervention or account review")
