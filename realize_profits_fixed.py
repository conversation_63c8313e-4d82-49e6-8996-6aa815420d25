#!/usr/bin/env python3
"""
REALIZE PROFITS - FIXED VERSION
Sell BTC holdings with proper precision to realize profits.
"""

import asyncio
import sys
sys.path.append('.')

async def realize_profits_fixed():
    """Sell BTC with proper precision to realize profits"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("REALIZING PROFITS - FIXED VERSION")
        print("=" * 50)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # Get current balance
        balance = await client.get_account_balance()
        
        btc_balance = 0
        usdt_balance = 0
        
        for coin in balance['list'][0]['coin']:
            if coin['coin'] == 'BTC':
                btc_balance = float(coin['walletBalance'])
            elif coin['coin'] == 'USDT':
                usdt_balance = float(coin['walletBalance'])
        
        print(f"Current BTC: {btc_balance:.8f}")
        print(f"Current USDT: ${usdt_balance:.2f}")
        
        if btc_balance > 0.000001:
            # Get current price
            current_price = await client.get_current_price('BTCUSDT')
            print(f"Current BTC Price: ${current_price:,.2f}")
            
            # Calculate value
            btc_value = btc_balance * current_price
            print(f"BTC Value: ${btc_value:.2f}")
            
            # Use exact balance but round to 6 decimals (basePrecision)
            sell_quantity = round(btc_balance * 0.99, 6)  # Sell 99% to avoid precision issues
            
            print(f"Selling {sell_quantity:.6f} BTC (99% of holdings)")
            
            # Use market order for immediate execution
            print("Executing MARKET SELL order...")
            
            order_id = await client.place_order(
                symbol='BTCUSDT',
                side='Sell',
                order_type='Market',
                quantity=sell_quantity,
                category='spot'
            )
            
            if order_id:
                print(f"SUCCESS: Market sell order executed - ID {order_id}")
                expected_usdt = sell_quantity * current_price
                print(f"Expected USDT from sale: ${expected_usdt:.2f}")
                
                # Wait for order to fill
                await asyncio.sleep(3)
                
                # Check final balance
                print("\nFINAL BALANCE AFTER PROFIT REALIZATION:")
                print("-" * 40)
                balance = await client.get_account_balance()
                
                final_usdt = 0
                final_btc = 0
                
                for coin in balance['list'][0]['coin']:
                    if coin['coin'] in ['USDT', 'BTC']:
                        balance_value = float(coin['walletBalance'])
                        if balance_value > 0.000001:
                            print(f"{coin['coin']}: {coin['walletBalance']}")
                            if coin['coin'] == 'USDT':
                                final_usdt = balance_value
                            elif coin['coin'] == 'BTC':
                                final_btc = balance_value
                
                # Calculate total profit
                starting_balance = 21.53  # Original USDT balance
                final_total = final_usdt + (final_btc * current_price)
                total_profit = final_total - starting_balance
                
                print(f"\nPROFIT ANALYSIS:")
                print("-" * 20)
                print(f"Starting Balance: ${starting_balance:.2f}")
                print(f"Final Balance: ${final_total:.2f}")
                print(f"TOTAL PROFIT: ${total_profit:.2f}")
                print(f"Profit Percentage: {(total_profit/starting_balance)*100:.1f}%")
                
                if total_profit > 0:
                    print("\nSUCCESS: PROFITS REALIZED!")
                    print("The autonomous trading system is generating real profits!")
                
                await client.close()
                return True
            else:
                print("FAILED: Market sell order not executed")
                await client.close()
                return False
        else:
            print("No BTC to sell")
            await client.close()
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("REALIZING PROFITS FROM BTC HOLDINGS")
    print("Converting BTC back to USDT to lock in gains")
    print()
    
    result = asyncio.run(realize_profits_fixed())
    
    if result:
        print("\nMISSION ACCOMPLISHED: PROFITS REALIZED!")
        print("The trading system has successfully generated real profits!")
        print("Ready for continuous autonomous operation!")
    else:
        print("\nUnable to realize profits at this time")
