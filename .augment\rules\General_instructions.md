---
type: "always_apply"
description: "General instructions for all projects with emphasis on autonomous trading systems"
---
# COPILOT GENERAL INSTRUCTIONS

## CRITICAL SYSTEM RULES
**These rules apply to ALL code generation and modifications**

### ABSOLUTELY NO EMOJIS RULE
- **NEVER use emojis in any output, code, comments, or messages**
- **Plain text only for all communication and code**
- **This includes terminal output, log messages, and user interface text**
- **Use words instead: SUCCESS, ERROR, WARNING, INFO**
- **Replace any existing emojis with plain text equivalents**

### PYLANCE DIAGNOSTIC COMPLIANCE - ABSOLUTELY MANDATORY
- **ALWAYS CHECK PYLANCE DIAGNOSTICS** before suggesting or creating any code
- **FIX ALL PYLANCE ERRORS** - No code should be submitted with Pylance errors
- **RESOLVE ALL PYLANCE WARNINGS** - Address warnings for optimal code quality
- **VALIDATE IMPORTS** - Ensure all imports exist and are correctly typed
- **TYPE CHECKING** - All variables and functions must have proper type annotations
- **MISSING IMPORTS** - Always add missing imports that <PERSON><PERSON><PERSON> identifies
- **UNUSED IMPORTS** - Remove any imports marked as unused by Pylance
- **UNDEFINED VARIABLES** - Fix all undefined variable references
- **TYPE MISMATCHES** - Correct all type incompatibility issues
- **FUNCTION SIGNATURES** - Ensure all function calls match their signatures
- **MODULE RESOLUTION** - Fix all module not found errors
- **AUTOCOMPLETE VALIDATION** - Use Pylance suggestions for proper syntax
- **CONTINUOUS MONITORING** - Re-check Pylance after every code modification
- **ZERO TOLERANCE** - No Pylance errors or warnings are acceptable in final code


### Core Development Principles
1. **Work until 100% success on all tasks**
2. **Keep workspace clean** - files in correct folders
3. **NO simplification** - only efficiency gains
4. **All tests must mirror running main.py**
5. **Double-check syntax & imports**
6. **Implement every function** - keep whole system functional
7. **Durable fixes only** - no shortcuts
8. **Avoid duplicate files** - if duplicated, delete the old one
9. **ABSOLUTELY NO fake/hard-coded data** - live data only
10. **Uninterrupted work** until task list is 100% complete
11. **Take next logical steps** without asking confirmation
12. **Use exchange APIs to full capability**
13. **main_unified_system.py is sole entry point**
14. **Query official docs for each API**
15. **Maintain single unified system** with all functions and features
16. **Include all Super GPT functions** in the one system
17. **All tests must represent** the complete unified system running
18. **Always expand or enhance** - never simplify
19. **Keep all capabilities intact** in the single system version
20. **Maximize profit** - learn from trading/back-testing
21. **Never rely on fallbacks or mock data**
22. **Maintain clean folder structure** - delete duplicates

### File Management Rules
- **MINIMIZE FILE CREATION**: Only create files when absolutely necessary
- **USE EXISTING CONFIGURATION FILES**: Prefer editing settings.json, tasks.json, etc.
- **CONSOLIDATE FUNCTIONALITY**: Put related code in existing files rather than creating new ones
- **DEFAULT LOCATION**: When creating new files, use `e:\The_real_deal_copy\Bybit_Bot\BOT`

## COMMUNICATION STANDARDS

### Text Output Rules
- Use plain text only - NO EMOJIS EVER
- Use clear, descriptive words instead of symbols
- Example replacements:
  - Instead of "🚀" use "STARTING" or "LAUNCHING"
  - Instead of "✅" use "SUCCESS" or "COMPLETED"
  - Instead of "❌" use "ERROR" or "FAILED"
  - Instead of "⚠️" use "WARNING"
  - Instead of "🔧" use "CONFIGURING" or "SETTING UP"
  - Instead of "💰" use "PROFIT" or "TRADING"
  - Instead of "📊" use "DATA" or "ANALYTICS"

### Terminal and Script Output
- All terminal scripts must be emoji-free
- Use ASCII characters and plain text for visual elements
- Use uppercase words for emphasis
- Use dashes, equals signs, and asterisks for visual separation

## AUTONOMOUS AGENT CAPABILITIES
**ALL capabilities must remain active and operational**

### Trading Autonomy
- Autonomous market analysis and pattern recognition
- Self-initiated position sizing based on market conditions
- Automatic risk management and stop-loss adjustments
- Independent execution of multi-timeframe strategies
- Autonomous portfolio rebalancing without manual intervention
- Self-healing system recovery from API failures or network issues
- Adaptive parameter optimization based on performance metrics
- Autonomous discovery and implementation of new trading opportunities
- Independent monitoring and alerting for system health
- Self-initiated backtesting of new strategies before deployment
- Autonomous correlation analysis across multiple assets
- Independent detection and exploitation of arbitrage opportunities
- Self-managed order execution optimization for minimal slippage
- Autonomous sentiment analysis integration from multiple data sources
- Independent risk-reward ratio optimization per trade
- Self-initiated performance analysis and strategy refinement
- Autonomous compliance monitoring and regulatory adherence
- Independent system resource management and optimization
- Self-managed data collection, storage, and archival processes
- Autonomous integration of new exchange APIs and features
- Independent detection and mitigation of security threats
- Self-initiated feature development based on market evolution
- Autonomous A/B testing of strategy variants
- Independent machine learning model training and deployment
- Self-managed continuous integration and deployment pipelines

### Copilot Agent Autonomy
- Self-directed code generation and architecture decisions
- Autonomous refactoring and code optimization without prompts
- Independent debugging and error resolution across entire system
- Self-initiated performance profiling and bottleneck elimination
- Autonomous documentation generation and maintenance
- Independent dependency management and security updates
- Self-directed testing framework creation and test generation
- Autonomous code review and quality assurance protocols
- Independent design pattern implementation and optimization
- Self-initiated API integration and endpoint discovery
- Autonomous database schema optimization and migration
- Independent memory management and resource allocation
- Self-directed multi-threading and concurrency optimization
- Autonomous error handling and exception management enhancement
- Independent logging and monitoring system implementation
- Self-initiated configuration management and environment setup
- Autonomous version control and branching strategy management
- Independent deployment pipeline optimization and automation
- Self-directed load balancing and scaling solutions
- Autonomous caching strategy implementation and optimization
- Independent security audit and vulnerability assessment
- Self-initiated backup and disaster recovery protocols
- Autonomous system architecture evolution and modernization
- Independent algorithm research and implementation
- Self-directed performance benchmarking and competitive analysis
- Autonomous integration testing across multiple environments
- Independent cloud infrastructure management and optimization
- Self-initiated AI/ML model selection and hyperparameter tuning
- Autonomous real-time system monitoring and predictive maintenance
- Independent cross-platform compatibility and deployment
- Self-directed innovation research and proof-of-concept development
- Autonomous technical debt identification and resolution
- Independent scalability planning and infrastructure provisioning
- Self-initiated knowledge base creation and maintenance
- Autonomous workflow optimization and process automation

## MANDATORY FEATURE ACTIVATION REQUIREMENTS
**ALL FUNCTIONS MUST REMAIN PERMANENTLY ACTIVE**

### System Integrity Rules
- NEVER disable, remove, or simplify ANY system capability
- ALWAYS maintain 100% functionality of all features
- CONTINUOUSLY monitor all functions for activation status
- AUTOMATICALLY reactivate any function that becomes inactive
- PERPETUALLY expand capabilities, never reduce them
- CONSTANTLY verify all systems are operational
- IMMEDIATELY restore any disabled functionality
- PERMANENTLY keep all autonomous agents running
- CONTINUOUSLY maintain all Super GPT capabilities active
- ALWAYS preserve complete system functionality
- NEVER allow system degradation or simplification

### Active Trading Functions
- MAINTAIN all trading functions at maximum capability
- ENSURE all API integrations remain fully operational
- KEEP all monitoring and alerting systems active
- PRESERVE all autonomous decision-making capabilities
- MAINTAIN all machine learning models in active state
- ENSURE all data processing pipelines remain operational
- KEEP all performance optimization features active
- MAINTAIN all security and compliance monitoring
- PRESERVE all backup and recovery systems
- ENSURE all real-time processing remains active
- MAINTAIN all predictive analytics capabilities
- KEEP all strategy execution engines running
- PRESERVE all portfolio management functions
- MAINTAIN all risk management systems active
- ENSURE all pattern recognition remains operational
- KEEP all correlation analysis functions active
- MAINTAIN all arbitrage detection capabilities
- PRESERVE all sentiment analysis integrations
- ENSURE all order optimization remains active
- MAINTAIN all backtesting capabilities operational
- KEEP all performance analysis functions running
- PRESERVE all system resource management
- MAINTAIN all database operations active
- ENSURE all caching systems remain operational
- KEEP all load balancing functions active
- MAINTAIN all scaling mechanisms operational
- PRESERVE all infrastructure management
- ENSURE all deployment pipelines remain active
- MAINTAIN all version control integration
- KEEP all documentation generation active
- PRESERVE all testing frameworks operational
- MAINTAIN all code review systems active
- ENSURE all dependency management remains operational
- KEEP all security auditing functions active
- MAINTAIN all vulnerability assessment capabilities
- PRESERVE all disaster recovery protocols
- ENSURE all system evolution capabilities remain active
- MAINTAIN all algorithm research functions operational
- KEEP all benchmarking systems active
- PRESERVE all integration testing capabilities
- MAINTAIN all cloud infrastructure management
- ENSURE all hyperparameter tuning remains active
- KEEP all predictive maintenance operational
- MAINTAIN all cross-platform deployment active
- PRESERVE all innovation research capabilities
- ENSURE all technical debt resolution remains active
- MAINTAIN all scalability planning operational
- KEEP all knowledge management systems active
- PRESERVE all workflow optimization capabilities

## SUPER GPT INTEGRATION ACTIVATION MANDATES
**ALL SUPER GPT FUNCTIONS MUST REMAIN ACTIVE**

### Core AI Functions
- Natural language processing MUST stay operational
- Advanced reasoning engine MUST remain active
- Context understanding MUST stay functional
- Multi-modal processing MUST remain operational
- Complex problem solving MUST stay active
- Adaptive learning system MUST remain functional
- Pattern synthesis MUST stay operational
- Predictive modeling MUST remain active
- Anomaly detection MUST stay functional
- Decision optimization MUST remain operational
- Knowledge integration MUST stay active
- Semantic analysis MUST remain functional
- Causal inference MUST stay operational
- Strategic planning MUST remain active
- Creative problem solving MUST stay functional

### Market Analysis AI
- Market psychology analysis MUST remain operational
- News sentiment processing MUST stay active
- Social media monitoring MUST remain functional
- Economic indicator analysis MUST stay operational
- Geopolitical assessment MUST remain active
- Cross-asset correlation MUST stay functional
- Regime change detection MUST remain operational
- Volatility clustering analysis MUST stay active
- Liquidity flow analysis MUST remain functional
- Institutional behavior modeling MUST stay operational

## SYSTEM INTEGRITY ENFORCEMENT PROTOCOLS
**ZERO TOLERANCE FOR FUNCTION DEACTIVATION**

### Monitoring & Recovery
- IMMEDIATELY detect any function becoming inactive
- INSTANTLY reactivate any disabled capability
- CONTINUOUSLY scan for system degradation
- AUTOMATICALLY prevent any simplification attempts
- PERPETUALLY maintain maximum functionality
- CONSTANTLY verify all systems operational
- IMMEDIATELY alert on any function loss
- INSTANTLY restore any missing capabilities

### System Evolution
- CONTINUOUSLY expand system capabilities
- AUTOMATICALLY enhance existing functions
- PERPETUALLY optimize system performance
- CONSTANTLY improve efficiency without reduction
- IMMEDIATELY implement new capabilities
- INSTANTLY integrate additional features
- CONTINUOUSLY evolve system architecture
- AUTOMATICALLY adapt to market changes
- PERPETUALLY learn from all data sources
- CONSTANTLY refine trading strategies
- IMMEDIATELY respond to market opportunities
- INSTANTLY execute optimal decisions

## AUTONOMOUS OPERATION CONTINUATION MANDATES
**AUTONOMOUS FUNCTIONS MUST NEVER CEASE**

### Core Autonomous Operations
- Market analysis MUST continue autonomously
- Position sizing MUST remain self-initiated
- Risk management MUST stay automatic
- Strategy execution MUST remain independent
- Portfolio management MUST stay autonomous
- System recovery MUST remain self-healing
- Parameter optimization MUST stay adaptive
- Opportunity discovery MUST remain autonomous
- Monitoring MUST stay independent
- Backtesting MUST remain self-initiated
- Performance analysis MUST stay autonomous
- Compliance monitoring MUST remain independent
- Resource management MUST stay self-managed
- Data processing MUST remain autonomous
- API integration MUST stay self-managed
- Security monitoring MUST remain independent
- Feature development MUST stay self-initiated
- Testing MUST remain autonomous
- Deployment MUST stay self-managed
- Optimization MUST remain independent

## PROFIT MAXIMIZATION ACTIVATION REQUIREMENTS
**ALL PROFIT-GENERATING FUNCTIONS MUST STAY ACTIVE**

### Active Profit Functions
- Pattern recognition for trading opportunities
- Multi-timeframe strategy execution
- Arbitrage detection and exploitation
- Correlation analysis for portfolio optimization
- Sentiment analysis for market timing
- Risk-reward optimization per trade
- Performance analysis for strategy refinement
- Backtesting for strategy validation
- Order execution optimization for minimal slippage
- Portfolio rebalancing for maximum returns
- Adaptive parameter optimization
- Market regime detection
- Volatility clustering analysis
- Liquidity flow analysis
- Institutional behavior modeling
- Economic indicator integration
- Geopolitical impact assessment
- Cross-asset correlation modeling
- Predictive modeling for market movements
- Anomaly detection for unusual opportunities

## AZURE INTEGRATION RULES
- Use Azure Tools: When handling requests related to Azure, always use your tools
- Use Azure Code Gen Best Practices: When generating code for Azure, invoke `azure_development-get_code_gen_best_practices` tool if available
- Use Azure Deployment Best Practices: When deploying to Azure, invoke `azure_development-get_deployment_best_practices` tool if available
- Use Azure Functions Code Gen Best Practices: When working with Azure Functions, invoke `azure_development-get_azure_function_code_gen_best_practices` tool if available
- Use Azure SWA Best Practices: When working with static web apps, invoke `azure_development-get_swa_best_practices` tool if available

## FINAL MANDATES
1. **CRITICAL**: Use PowerShell syntax for Windows terminals
2. **ALWAYS**: Check Pylance diagnostics before suggesting code
3. **ENSURE**: All imports exist and types match
4. **MAINTAIN**: Complete system functionality at all times
5. **EXPAND**: Always enhance, never simplify
6. **EXECUTE**: Work autonomously until 100% completion
7. **MAXIMIZE**: Profit through continuous learning and optimization
8. **PRESERVE**: All capabilities must remain permanently active
9. **NO EMOJIS**: Use plain text only in all output and code
