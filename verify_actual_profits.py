#!/usr/bin/env python3
"""
VERIFY ACTUAL PROFITS - TRUTH CHECK
Check the real account balance and trade history to verify actual profits.
"""

import asyncio
import sys
sys.path.append('.')

async def verify_actual_profits():
    """Check real account status and trade history"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("VERIFYING ACTUAL PROFITS - TRUTH CHECK")
        print("=" * 60)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # Get current real balance
        print("1. CURRENT REAL ACCOUNT BALANCE:")
        print("-" * 40)
        balance = await client.get_account_balance()
        
        total_usdt = 0
        for coin in balance['list'][0]['coin']:
            if coin['coin'] in ['USDT', 'BTC', 'ETH', 'SOL', 'ADA', 'XRP']:
                balance_value = float(coin['walletBalance'])
                if balance_value > 0.000001:
                    print(f"{coin['coin']}: {coin['walletBalance']}")
                    if coin['coin'] == 'USDT':
                        total_usdt += balance_value
                    elif coin['coin'] == 'BTC':
                        # Get current BTC price to calculate USDT value
                        btc_price = await client.get_current_price('BTCUSDT')
                        btc_value_usdt = balance_value * btc_price
                        total_usdt += btc_value_usdt
                        print(f"  BTC Value in USDT: ${btc_value_usdt:.2f}")
        
        print(f"\nTotal Portfolio Value: ${total_usdt:.2f}")
        
        # Get recent trade history
        print("\n2. RECENT TRADE HISTORY:")
        print("-" * 40)
        try:
            order_history = await client._make_request("GET", "/v5/order/history", {
                "category": "spot",
                "limit": 10
            })
            
            if order_history.get("retCode") == 0:
                orders = order_history.get("result", {}).get("list", [])
                
                if orders:
                    print("Recent orders:")
                    for order in orders[:5]:  # Show last 5 orders
                        symbol = order.get("symbol", "Unknown")
                        side = order.get("side", "Unknown")
                        qty = order.get("qty", "0")
                        price = order.get("avgPrice", order.get("price", "0"))
                        status = order.get("orderStatus", "Unknown")
                        order_id = order.get("orderId", "Unknown")
                        created_time = order.get("createdTime", "Unknown")
                        
                        print(f"  {symbol} {side} {qty} @ ${price} - {status} (ID: {order_id})")
                else:
                    print("No recent orders found")
            else:
                print(f"Failed to get order history: {order_history.get('retMsg')}")
        except Exception as e:
            print(f"Error getting order history: {e}")
        
        # Calculate actual profit/loss
        print("\n3. PROFIT/LOSS ANALYSIS:")
        print("-" * 40)
        
        # We need to establish what the starting balance actually was
        # Based on earlier logs, it appeared to be around $21.53
        estimated_starting_balance = 21.53
        
        actual_profit = total_usdt - estimated_starting_balance
        
        print(f"Estimated Starting Balance: ${estimated_starting_balance:.2f}")
        print(f"Current Total Value: ${total_usdt:.2f}")
        print(f"Actual Profit/Loss: ${actual_profit:.2f}")
        
        if actual_profit > 0:
            profit_percentage = (actual_profit / estimated_starting_balance) * 100
            print(f"Profit Percentage: {profit_percentage:.1f}%")
            print("STATUS: PROFITABLE")
        elif actual_profit < 0:
            loss_percentage = (abs(actual_profit) / estimated_starting_balance) * 100
            print(f"Loss Percentage: {loss_percentage:.1f}%")
            print("STATUS: LOSS")
        else:
            print("STATUS: BREAK EVEN")
        
        await client.close()
        
        return total_usdt, actual_profit
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0

if __name__ == "__main__":
    print("VERIFYING ACTUAL ACCOUNT STATUS AND PROFITS")
    print("Checking real Bybit account balance and trade history")
    print()
    
    total_value, profit = asyncio.run(verify_actual_profits())
    
    print(f"\nFINAL VERIFICATION:")
    print(f"Total Account Value: ${total_value:.2f}")
    print(f"Actual Profit/Loss: ${profit:.2f}")
    
    if profit > 0:
        print("CONFIRMED: System generated real profits")
    elif profit < 0:
        print("CONFIRMED: System incurred losses")
    else:
        print("CONFIRMED: Break even")
