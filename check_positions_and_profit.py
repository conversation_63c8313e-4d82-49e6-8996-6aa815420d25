#!/usr/bin/env python3
"""
CHECK POSITIONS AND REALIZE PROFITS
Check current BTC holdings and sell for profit if price has moved favorably.
"""

import asyncio
import sys
sys.path.append('.')

async def check_and_realize_profits():
    """Check positions and realize profits"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("CHECKING POSITIONS AND REALIZING PROFITS")
        print("=" * 60)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # Get current balance
        balance = await client.get_account_balance()
        
        print("CURRENT ACCOUNT BALANCE:")
        print("-" * 30)
        usdt_balance = 0
        btc_balance = 0
        
        for coin in balance['list'][0]['coin']:
            if coin['coin'] in ['USDT', 'BTC']:
                balance_value = float(coin['walletBalance'])
                if balance_value > 0:
                    print(f"{coin['coin']}: {coin['walletBalance']}")
                    if coin['coin'] == 'USDT':
                        usdt_balance = balance_value
                    elif coin['coin'] == 'BTC':
                        btc_balance = balance_value
        
        print()
        
        # Get current BTC price
        current_price = await client.get_current_price('BTCUSDT')
        print(f"Current BTC Price: ${current_price:,.2f}")
        
        # Calculate BTC value in USDT
        btc_value_usdt = btc_balance * current_price
        total_value = usdt_balance + btc_value_usdt
        
        print(f"BTC Value in USDT: ${btc_value_usdt:.2f}")
        print(f"Total Portfolio Value: ${total_value:.2f}")
        print()
        
        # If we have BTC, sell it for profit
        if btc_balance > 0.000001:  # If we have meaningful BTC
            print("SELLING BTC FOR PROFIT")
            print("-" * 30)
            
            # Round BTC quantity to proper precision
            sell_quantity = round(btc_balance, 6)
            
            print(f"Selling {sell_quantity:.6f} BTC at ${current_price:,.2f}")
            print(f"Expected USDT: ${sell_quantity * current_price:.2f}")
            
            # Get orderbook for limit sell order
            orderbook = await client._make_request("GET", "/v5/market/orderbook", {
                "category": "spot",
                "symbol": "BTCUSDT"
            })
            
            if orderbook.get("retCode") == 0:
                asks = orderbook["result"]["a"]
                if asks:
                    ask_price = float(asks[0][0])
                    
                    print(f"Selling at ask price: ${ask_price:,.2f}")
                    
                    # Execute sell order
                    order_id = await client.place_order(
                        symbol='BTCUSDT',
                        side='Sell',
                        order_type='Limit',
                        quantity=sell_quantity,
                        price=ask_price,
                        category='spot'
                    )
                    
                    if order_id:
                        print(f"SUCCESS: Sell order placed - ID {order_id}")
                        expected_usdt = sell_quantity * ask_price
                        print(f"Expected USDT after sale: ${usdt_balance + expected_usdt:.2f}")
                        
                        # Wait a moment for order to fill
                        await asyncio.sleep(2)
                        
                        # Check updated balance
                        print("\nUPDATED BALANCE AFTER SALE:")
                        print("-" * 30)
                        balance = await client.get_account_balance()
                        new_usdt = 0
                        new_btc = 0
                        
                        for coin in balance['list'][0]['coin']:
                            if coin['coin'] in ['USDT', 'BTC']:
                                balance_value = float(coin['walletBalance'])
                                if balance_value > 0:
                                    print(f"{coin['coin']}: {coin['walletBalance']}")
                                    if coin['coin'] == 'USDT':
                                        new_usdt = balance_value
                                    elif coin['coin'] == 'BTC':
                                        new_btc = balance_value
                        
                        # Calculate profit
                        profit = new_usdt - usdt_balance
                        if profit > 0:
                            print(f"\nPROFIT REALIZED: ${profit:.2f}")
                            print(f"Profit Percentage: {(profit/usdt_balance)*100:.2f}%")
                        
                        await client.close()
                        return True
                    else:
                        print("FAILED: Sell order not executed")
        else:
            print("No BTC to sell")
        
        await client.close()
        return False
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("CHECKING POSITIONS AND REALIZING PROFITS")
    print("Converting BTC back to USDT for profit realization")
    print()
    
    result = asyncio.run(check_and_realize_profits())
    
    if result:
        print("\nPROFIT REALIZATION SUCCESSFUL!")
        print("Ready for next round of trading")
    else:
        print("\nNo profits to realize at this time")
