#!/usr/bin/env python3
"""
FORCE TRADE WITH EXACT MINIMUM REQUIREMENTS
Use the exact minimum values from the instrument info.
"""

import asyncio
import sys
sys.path.append('.')

async def force_exact_minimum_trade():
    """Execute trade with exact minimum requirements"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("FORCING TRADE WITH EXACT MINIMUM REQUIREMENTS")
        print("=" * 60)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # Get current BTC price
        current_price = await client.get_current_price('BTCUSDT')
        print(f"Current BTC Price: ${current_price:,.2f}")
        
        # Use EXACT minimum values from instrument info
        min_order_qty = 0.000011  # Exact minimum from API
        min_order_amt = 5.0       # Exact minimum from API

        # Calculate which constraint is more restrictive
        qty_value = min_order_qty * current_price

        if qty_value >= min_order_amt:
            # Use minimum quantity
            quantity = min_order_qty
            trade_value = qty_value
        else:
            # Use minimum amount
            quantity = min_order_amt / current_price
            trade_value = min_order_amt

        # Round to proper precision (basePrecision is 0.000001, so 6 decimal places)
        quantity = round(quantity, 6)
        
        print(f"Using minimum quantity: {quantity:.6f} BTC")
        print(f"Trade value: ${trade_value:.2f} USDT")
        
        # Verify we meet both minimums
        if quantity < min_order_qty:
            print(f"ERROR: Quantity {quantity:.6f} is below minimum {min_order_qty}")
            return False
            
        if trade_value < min_order_amt:
            print(f"ERROR: Value ${trade_value:.2f} is below minimum ${min_order_amt}")
            return False
        
        print("SUCCESS: Both minimum requirements satisfied")
        print()

        # Execute the trade
        print("EXECUTING REAL TRADE WITH EXACT MINIMUMS...")
        order_id = await client.place_order(
            symbol='BTCUSDT',
            side='Buy',
            order_type='Market',
            quantity=quantity,
            category='spot'
        )

        if order_id:
            print("SUCCESS: REAL TRADE EXECUTED!")
            print(f"Order ID: {order_id}")
            print(f"BTC Purchased: {quantity:.6f}")
            print(f"USDT Spent: ${trade_value:.2f}")

            # Verify with balance check
            print("\nVerifying trade execution...")
            balance = await client.get_account_balance()
            for coin in balance['list'][0]['coin']:
                if coin['coin'] in ['USDT', 'BTC']:
                    print(f"{coin['coin']} Balance: {coin['walletBalance']}")

            await client.close()
            return True
        else:
            print("FAILED: Trade was not executed")
            await client.close()
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("EXECUTING TRADE WITH EXACT MINIMUM REQUIREMENTS")
    print("This uses the exact minimum values from Bybit instrument info")
    print()
    
    result = asyncio.run(force_exact_minimum_trade())
    
    if result:
        print("\nMISSION ACCOMPLISHED: REAL TRADE EXECUTED SUCCESSFULLY!")
        print("The trading system is now proven to work with real money!")
    else:
        print("\nMISSION FAILED: Unable to execute real trades")
        print("There may be account-level restrictions preventing trading")
