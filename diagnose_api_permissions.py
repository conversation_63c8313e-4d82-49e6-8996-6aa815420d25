#!/usr/bin/env python3
"""
DIAGNOSE API PERMISSIONS AND ACCOUNT STATUS
This script will check what's preventing trade execution.
"""

import asyncio
import sys
import json
sys.path.append('.')

async def diagnose_api_issues():
    """Comprehensive API and account diagnosis"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("API PERMISSIONS AND ACCOUNT DIAGNOSIS")
        print("=" * 60)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # 1. Check basic connectivity
        print("1. BASIC CONNECTIVITY TEST")
        print("-" * 30)
        try:
            # Get server time (no auth required)
            response = await client._make_request("GET", "/v5/market/time")
            if response.get("retCode") == 0:
                print("✅ Server connectivity: OK")
            else:
                print(f"❌ Server connectivity: FAILED - {response}")
        except Exception as e:
            print(f"❌ Server connectivity: ERROR - {e}")
        
        # 2. Check API key authentication
        print("\n2. API KEY AUTHENTICATION TEST")
        print("-" * 30)
        try:
            balance = await client.get_account_balance()
            if balance and 'list' in balance:
                print("✅ API authentication: OK")
                print("Account balances:")
                for coin in balance['list'][0]['coin']:
                    if float(coin['walletBalance']) > 0:
                        print(f"  {coin['coin']}: {coin['walletBalance']}")
            else:
                print(f"❌ API authentication: FAILED - {balance}")
        except Exception as e:
            print(f"❌ API authentication: ERROR - {e}")
        
        # 3. Check API permissions by trying different endpoints
        print("\n3. API PERMISSIONS TEST")
        print("-" * 30)
        
        # Test read permissions
        try:
            orderbook = await client._make_request("GET", "/v5/market/orderbook", {"category": "spot", "symbol": "BTCUSDT"})
            if orderbook.get("retCode") == 0:
                print("✅ Market data (read): OK")
            else:
                print(f"❌ Market data (read): FAILED - {orderbook.get('retMsg')}")
        except Exception as e:
            print(f"❌ Market data (read): ERROR - {e}")
        
        # Test account permissions
        try:
            wallet = await client._make_request("GET", "/v5/account/wallet-balance", {"accountType": "UNIFIED"})
            if wallet.get("retCode") == 0:
                print("✅ Account data (account): OK")
            else:
                print(f"❌ Account data (account): FAILED - {wallet.get('retMsg')}")
        except Exception as e:
            print(f"❌ Account data (account): ERROR - {e}")
        
        # 4. Test trading permissions with a minimal order
        print("\n4. TRADING PERMISSIONS TEST")
        print("-" * 30)
        
        # Get current BTC price first
        try:
            ticker = await client._make_request("GET", "/v5/market/tickers", {"category": "spot", "symbol": "BTCUSDT"})
            if ticker.get("retCode") == 0 and ticker.get("result", {}).get("list"):
                current_price = float(ticker["result"]["list"][0]["lastPrice"])
                print(f"Current BTC price: ${current_price:,.2f}")
                
                # Try multiple order sizes and symbols
                test_cases = [
                    {"symbol": "BTCUSDT", "value": 50.0},  # $50 BTC
                    {"symbol": "ETHUSDT", "value": 25.0},  # $25 ETH
                    {"symbol": "SOLUSDT", "value": 20.0},  # $20 SOL
                    {"symbol": "BTCUSDT", "value": 100.0}, # $100 BTC
                ]

                for test_case in test_cases:
                    symbol = test_case["symbol"]
                    trade_value = test_case["value"]

                    # Get price for this symbol
                    ticker = await client._make_request("GET", "/v5/market/tickers", {"category": "spot", "symbol": symbol})
                    if ticker.get("retCode") != 0 or not ticker.get("result", {}).get("list"):
                        continue

                    current_price = float(ticker["result"]["list"][0]["lastPrice"])
                    quantity = trade_value / current_price
                    quantity = round(quantity, 6)

                    print(f"\nTesting {symbol}: ${trade_value} USDT = {quantity:.6f} {symbol.replace('USDT', '')}")

                    order_data = {
                        "category": "spot",
                        "symbol": symbol,
                        "side": "Buy",
                        "orderType": "Market",
                        "qty": str(quantity)
                    }

                    print(f"Order data: {json.dumps(order_data, indent=2)}")

                    order_response = await client._make_request("POST", "/v5/order/create", order_data)

                    if order_response.get("retCode") == 0:
                        order_id = order_response.get("result", {}).get("orderId")
                        print(f"✅ SUCCESS: Order placed: {order_id}")
                        print(f"🎉 REAL TRADE EXECUTED: {symbol} for ${trade_value}")
                        await client.close()
                        return True
                    else:
                        print(f"❌ FAILED: {order_response.get('retMsg')} (Code: {order_response.get('retCode')})")

                # If all test cases failed
                print(f"\n❌ ALL TEST CASES FAILED - No successful trades")
                return False
            else:
                print("❌ Could not get BTC price for trading test")
                return False
                
        except Exception as e:
            print(f"❌ Trading test: ERROR - {e}")
            import traceback
            traceback.print_exc()
            return False
        
        await client.close()
        
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("COMPREHENSIVE API DIAGNOSIS")
    print("This will test all API permissions and attempt a real trade")
    print()
    
    result = asyncio.run(diagnose_api_issues())
    
    if result:
        print("\n🎉 SUCCESS: API has trading permissions and real trade executed!")
    else:
        print("\n❌ FAILURE: API permissions or account issues prevent trading")
