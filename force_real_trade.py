#!/usr/bin/env python3
"""
FORCE REAL TRADE EXECUTION - NO EXCUSES
This script will execute a real trade with real money or fail trying.
"""

import asyncio
import sys
sys.path.append('.')

async def execute_real_trade():
    """Execute a real trade with real money - NO SIMULATIONS"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("INITIALIZING REAL TRADE EXECUTION")
        print("=" * 50)
        
        # Initialize client
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # Get current BTC price
        print("Getting current BTC price...")
        current_price = await client.get_current_price('BTCUSDT')
        
        if current_price <= 0:
            print("FAILED: Could not get BTC price")
            return False
            
        print(f"Current BTC Price: ${current_price:,.2f}")
        
        # Calculate trade parameters - CORRECT MINIMUM VALUE
        trade_value = 6.0  # $6 USDT (above 5 USDT minimum for BTCUSDT)
        quantity = trade_value / current_price

        # Ensure minimum quantity (0.000011 BTC) and round to 6 decimal places
        min_quantity = 0.000011
        if quantity < min_quantity:
            quantity = min_quantity
            trade_value = quantity * current_price

        quantity = round(quantity, 6)

        print(f"Trade Value: ${trade_value} USDT")
        print(f"BTC Quantity: {quantity:.6f}")
        print()
        
        # EXECUTE REAL TRADE
        print("EXECUTING REAL TRADE WITH REAL MONEY...")
        print("This will spend real USDT to buy real BTC")
        
        order_id = await client.place_order(
            symbol='BTCUSDT',
            side='Buy',
            order_type='Market',
            quantity=quantity,
            category='spot'
        )
        
        if order_id:
            print("SUCCESS: REAL TRADE EXECUTED!")
            print(f"Order ID: {order_id}")
            print(f"Spent: ${trade_value} USDT")
            print(f"Received: {quantity:.8f} BTC")
            
            # Get updated balance
            print("\nChecking updated balance...")
            balance = await client.get_account_balance()
            
            if balance and 'list' in balance and len(balance['list']) > 0:
                coins = balance['list'][0].get('coin', [])
                for coin in coins:
                    if coin['coin'] in ['USDT', 'BTC']:
                        print(f"{coin['coin']} Balance: {coin['walletBalance']}")
            
            await client.close()
            return True
            
        else:
            print("FAILED: Trade was not executed")
            await client.close()
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("FORCING REAL TRADE EXECUTION")
    print("This will use real money on live Bybit account")
    print()
    
    result = asyncio.run(execute_real_trade())
    
    if result:
        print("\nMISSION ACCOMPLISHED: REAL TRADE EXECUTED")
        print("The system successfully traded real money")
    else:
        print("\nMISSION FAILED: NO REAL TRADE EXECUTED")
        print("The system failed to execute real trades")
