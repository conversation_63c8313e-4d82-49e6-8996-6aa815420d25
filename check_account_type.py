#!/usr/bin/env python3
"""
CHECK ACCOUNT TYPE AND TRADING RESTRICTIONS
This will determine if the account is demo, live, or has restrictions.
"""

import asyncio
import sys
import json
sys.path.append('.')

async def check_account_type():
    """Check if this is a demo account or has trading restrictions"""
    
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import BotConfig
        
        print("ACCOUNT TYPE AND RESTRICTIONS CHECK")
        print("=" * 60)
        
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        # 1. Check account info
        print("1. ACCOUNT INFORMATION")
        print("-" * 30)
        try:
            account_info = await client._make_request("GET", "/v5/account/info")
            if account_info.get("retCode") == 0:
                result = account_info.get("result", {})
                print(f"Account Type: {result.get('unifiedMarginStatus', 'Unknown')}")
                print(f"Account Status: {result.get('status', 'Unknown')}")
                print(f"Margin Mode: {result.get('marginMode', 'Unknown')}")
                print(f"Updated Time: {result.get('updatedTime', 'Unknown')}")
                print(f"Full Account Info: {json.dumps(result, indent=2)}")
            else:
                print(f"Failed to get account info: {account_info}")
        except Exception as e:
            print(f"Error getting account info: {e}")
        
        # 2. Check wallet balance details
        print("\n2. WALLET BALANCE DETAILS")
        print("-" * 30)
        try:
            wallet = await client._make_request("GET", "/v5/account/wallet-balance", {"accountType": "UNIFIED"})
            if wallet.get("retCode") == 0:
                result = wallet.get("result", {})
                print(f"Account Type: {result.get('list', [{}])[0].get('accountType', 'Unknown')}")
                print(f"Total Equity: {result.get('list', [{}])[0].get('totalEquity', 'Unknown')}")
                print(f"Account LTV: {result.get('list', [{}])[0].get('accountLTV', 'Unknown')}")
                print(f"Account IM: {result.get('list', [{}])[0].get('accountIM', 'Unknown')}")
                print(f"Account MM: {result.get('list', [{}])[0].get('accountMM', 'Unknown')}")
            else:
                print(f"Failed to get wallet balance: {wallet}")
        except Exception as e:
            print(f"Error getting wallet balance: {e}")
        
        # 3. Check trading fee rate (this can indicate account type)
        print("\n3. TRADING FEE RATE CHECK")
        print("-" * 30)
        try:
            fee_rate = await client._make_request("GET", "/v5/account/fee-rate", {"category": "spot", "symbol": "BTCUSDT"})
            if fee_rate.get("retCode") == 0:
                result = fee_rate.get("result", {})
                print(f"Maker Fee Rate: {result.get('list', [{}])[0].get('makerFeeRate', 'Unknown')}")
                print(f"Taker Fee Rate: {result.get('list', [{}])[0].get('takerFeeRate', 'Unknown')}")
                print(f"Full Fee Info: {json.dumps(result, indent=2)}")
            else:
                print(f"Failed to get fee rate: {fee_rate}")
        except Exception as e:
            print(f"Error getting fee rate: {e}")
        
        # 4. Check if this is testnet/demo
        print("\n4. ENVIRONMENT CHECK")
        print("-" * 30)
        print(f"Base URL: {client.base_url}")
        if "testnet" in client.base_url or "demo" in client.base_url:
            print("🚨 THIS IS A TESTNET/DEMO ACCOUNT!")
            print("This explains why trades are failing - demo accounts may have different minimums")
        else:
            print("✅ This appears to be a LIVE production account")
        
        # 5. Check instrument info for minimum order values
        print("\n5. INSTRUMENT MINIMUM ORDER VALUES")
        print("-" * 30)
        try:
            instruments = await client._make_request("GET", "/v5/market/instruments-info", {"category": "spot", "symbol": "BTCUSDT"})
            if instruments.get("retCode") == 0:
                result = instruments.get("result", {})
                if result.get("list"):
                    instrument = result["list"][0]
                    print(f"Symbol: {instrument.get('symbol')}")
                    print(f"Min Order Qty: {instrument.get('lotSizeFilter', {}).get('minOrderQty', 'Unknown')}")
                    print(f"Max Order Qty: {instrument.get('lotSizeFilter', {}).get('maxOrderQty', 'Unknown')}")
                    print(f"Qty Step: {instrument.get('lotSizeFilter', {}).get('qtyStep', 'Unknown')}")
                    print(f"Min Order Amount: {instrument.get('lotSizeFilter', {}).get('minOrderAmt', 'Unknown')}")
                    print(f"Max Order Amount: {instrument.get('lotSizeFilter', {}).get('maxOrderAmt', 'Unknown')}")
                    print(f"Full Instrument Info: {json.dumps(instrument, indent=2)}")
            else:
                print(f"Failed to get instrument info: {instruments}")
        except Exception as e:
            print(f"Error getting instrument info: {e}")
        
        await client.close()
        
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_account_type())
